const express = require('express');
const Conversation = require('../models/Conversation');
const Message = require('../models/Message');
const User = require('../models/User');
const { auth } = require('../middleware/auth');
const { validateConversation } = require('../middleware/validation');

const router = express.Router();

// @route   GET /api/conversations
// @desc    Get user's conversations
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const skip = (page - 1) * limit;

    const conversations = await Conversation.find({
      'participants.user': req.user._id,
      isActive: true
    })
    .populate('participants.user', 'username firstName lastName avatar status')
    .populate('lastMessage', 'content type sender createdAt')
    .populate('lastMessage.sender', 'username firstName lastName')
    .sort({ lastActivity: -1 })
    .skip(skip)
    .limit(parseInt(limit));

    // Calculate unread count for each conversation
    const conversationsWithUnread = await Promise.all(
      conversations.map(async (conv) => {
        const participant = conv.participants.find(
          p => p.user._id.toString() === req.user._id.toString()
        );
        
        const unreadCount = await Message.countDocuments({
          conversation: conv._id,
          createdAt: { $gt: participant.lastReadAt },
          sender: { $ne: req.user._id }
        });

        return {
          ...conv.toObject(),
          unreadCount
        };
      })
    );

    const total = await Conversation.countDocuments({
      'participants.user': req.user._id,
      isActive: true
    });

    res.json({
      conversations: conversationsWithUnread,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('Get conversations error:', error);
    res.status(500).json({
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/conversations
// @desc    Create new conversation
// @access  Private
router.post('/', auth, validateConversation, async (req, res) => {
  try {
    const { name, description, type, participants } = req.body;

    // Validate participants exist
    const participantUsers = await User.find({
      _id: { $in: participants },
      isActive: true
    });

    if (participantUsers.length !== participants.length) {
      return res.status(400).json({
        message: 'One or more participants not found or inactive'
      });
    }

    // For direct conversations, check if conversation already exists
    if (type === 'direct') {
      if (participants.length !== 1) {
        return res.status(400).json({
          message: 'Direct conversation must have exactly one other participant'
        });
      }

      const existingConversation = await Conversation.findOne({
        type: 'direct',
        'participants.user': { $all: [req.user._id, participants[0]] },
        isActive: true
      });

      if (existingConversation) {
        return res.status(400).json({
          message: 'Direct conversation already exists',
          conversationId: existingConversation._id
        });
      }
    }

    // Create conversation
    const conversation = new Conversation({
      name: type === 'group' ? name : undefined,
      description,
      type,
      createdBy: req.user._id,
      participants: [
        {
          user: req.user._id,
          role: type === 'group' ? 'admin' : 'member'
        },
        ...participants.map(userId => ({
          user: userId,
          role: 'member'
        }))
      ]
    });

    await conversation.save();

    // Populate the conversation
    await conversation.populate('participants.user', 'username firstName lastName avatar status');

    res.status(201).json({
      message: 'Conversation created successfully',
      conversation
    });
  } catch (error) {
    console.error('Create conversation error:', error);
    res.status(500).json({
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/conversations/:id
// @desc    Get conversation by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const conversation = await Conversation.findById(req.params.id)
      .populate('participants.user', 'username firstName lastName avatar status')
      .populate('lastMessage', 'content type sender createdAt')
      .populate('lastMessage.sender', 'username firstName lastName');

    if (!conversation) {
      return res.status(404).json({
        message: 'Conversation not found'
      });
    }

    // Check if user is participant
    if (!conversation.isParticipant(req.user._id)) {
      return res.status(403).json({
        message: 'Access denied. You are not a participant in this conversation.'
      });
    }

    res.json({ conversation });
  } catch (error) {
    console.error('Get conversation error:', error);
    res.status(500).json({
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   PUT /api/conversations/:id
// @desc    Update conversation
// @access  Private
router.put('/:id', auth, async (req, res) => {
  try {
    const { name, description } = req.body;
    
    const conversation = await Conversation.findById(req.params.id);

    if (!conversation) {
      return res.status(404).json({
        message: 'Conversation not found'
      });
    }

    // Check if user is participant
    if (!conversation.isParticipant(req.user._id)) {
      return res.status(403).json({
        message: 'Access denied. You are not a participant in this conversation.'
      });
    }

    // Check if user has admin role for group conversations
    if (conversation.type === 'group' && conversation.getUserRole(req.user._id) !== 'admin') {
      return res.status(403).json({
        message: 'Access denied. Admin privileges required.'
      });
    }

    if (name !== undefined) conversation.name = name;
    if (description !== undefined) conversation.description = description;

    await conversation.save();
    await conversation.populate('participants.user', 'username firstName lastName avatar status');

    res.json({
      message: 'Conversation updated successfully',
      conversation
    });
  } catch (error) {
    console.error('Update conversation error:', error);
    res.status(500).json({
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/conversations/:id/participants
// @desc    Add participant to conversation
// @access  Private
router.post('/:id/participants', auth, async (req, res) => {
  try {
    const { userId } = req.body;
    
    const conversation = await Conversation.findById(req.params.id);

    if (!conversation) {
      return res.status(404).json({
        message: 'Conversation not found'
      });
    }

    // Check if user is participant and has permission
    if (!conversation.isParticipant(req.user._id)) {
      return res.status(403).json({
        message: 'Access denied. You are not a participant in this conversation.'
      });
    }

    if (conversation.type === 'direct') {
      return res.status(400).json({
        message: 'Cannot add participants to direct conversations'
      });
    }

    // Check if user has permission to add participants
    const userRole = conversation.getUserRole(req.user._id);
    if (userRole !== 'admin' && !conversation.settings.allowMemberInvites) {
      return res.status(403).json({
        message: 'Access denied. Only admins can add participants.'
      });
    }

    // Check if user to add exists and is active
    const userToAdd = await User.findById(userId);
    if (!userToAdd || !userToAdd.isActive) {
      return res.status(404).json({
        message: 'User not found or inactive'
      });
    }

    // Check if user is already a participant
    if (conversation.isParticipant(userId)) {
      return res.status(400).json({
        message: 'User is already a participant'
      });
    }

    await conversation.addParticipant(userId);
    await conversation.populate('participants.user', 'username firstName lastName avatar status');

    res.json({
      message: 'Participant added successfully',
      conversation
    });
  } catch (error) {
    console.error('Add participant error:', error);
    res.status(500).json({
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   DELETE /api/conversations/:id/participants/:userId
// @desc    Remove participant from conversation
// @access  Private
router.delete('/:id/participants/:userId', auth, async (req, res) => {
  try {
    const conversation = await Conversation.findById(req.params.id);

    if (!conversation) {
      return res.status(404).json({
        message: 'Conversation not found'
      });
    }

    // Check if user is participant
    if (!conversation.isParticipant(req.user._id)) {
      return res.status(403).json({
        message: 'Access denied. You are not a participant in this conversation.'
      });
    }

    if (conversation.type === 'direct') {
      return res.status(400).json({
        message: 'Cannot remove participants from direct conversations'
      });
    }

    const userRole = conversation.getUserRole(req.user._id);
    const targetUserId = req.params.userId;

    // Users can remove themselves, admins can remove others
    if (targetUserId !== req.user._id.toString() && userRole !== 'admin') {
      return res.status(403).json({
        message: 'Access denied. You can only remove yourself or need admin privileges.'
      });
    }

    await conversation.removeParticipant(targetUserId);
    await conversation.populate('participants.user', 'username firstName lastName avatar status');

    res.json({
      message: 'Participant removed successfully',
      conversation
    });
  } catch (error) {
    console.error('Remove participant error:', error);
    res.status(500).json({
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
