{"name": "internal-messenger-client", "version": "1.0.0", "description": "Frontend for internal messenger application", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.1", "@mui/material": "^5.15.1", "@reduxjs/toolkit": "^2.0.1", "axios": "^1.6.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1", "socket.io-client": "^4.7.4", "react-scripts": "5.0.1", "emoji-picker-react": "^4.5.16", "react-dropzone": "^14.2.3", "date-fns": "^2.30.0", "react-infinite-scroll-component": "^6.1.0", "react-hot-toast": "^2.4.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}