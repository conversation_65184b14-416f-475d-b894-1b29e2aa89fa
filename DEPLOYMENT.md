# Deployment Guide

This guide covers different deployment options for the Internal Messenger application.

## Prerequisites

- Node.js 16+ and npm
- MongoDB database (local or cloud)
- Git (for version control)

## Environment Configuration

### Server Environment Variables

Create a `server/.env` file with the following variables:

```env
# Database Configuration
MONGODB_URI=mongodb://localhost:27017/internal-messenger

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# Server Configuration
PORT=5000
NODE_ENV=production

# CORS Configuration
CLIENT_URL=http://localhost:3000

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### Client Environment Variables

Create a `client/.env` file:

```env
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_SERVER_URL=http://localhost:5000
```

## Local Development

1. **Install dependencies:**
   ```bash
   # Windows
   install.bat
   
   # Linux/Mac
   chmod +x install.sh
   ./install.sh
   ```

2. **Configure environment:**
   ```bash
   cp server/.env.example server/.env
   # Edit server/.env with your configuration
   ```

3. **Start MongoDB:**
   ```bash
   # If using local MongoDB
   mongod
   ```

4. **Start the application:**
   ```bash
   # Windows
   start.bat
   
   # Linux/Mac
   chmod +x start.sh
   ./start.sh
   ```

## Production Deployment

### Option 1: Traditional Server Deployment

1. **Prepare the server:**
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y
   
   # Install Node.js
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   
   # Install MongoDB
   wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
   echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
   sudo apt-get update
   sudo apt-get install -y mongodb-org
   
   # Install PM2 for process management
   sudo npm install -g pm2
   ```

2. **Deploy the application:**
   ```bash
   # Clone repository
   git clone <your-repo-url>
   cd internal-messenger
   
   # Install dependencies
   npm run install-all
   
   # Build client
   cd client && npm run build && cd ..
   
   # Configure environment
   cp server/.env.example server/.env
   # Edit server/.env with production values
   ```

3. **Start with PM2:**
   ```bash
   # Create PM2 ecosystem file
   cat > ecosystem.config.js << EOF
   module.exports = {
     apps: [{
       name: 'internal-messenger',
       script: 'server/index.js',
       instances: 'max',
       exec_mode: 'cluster',
       env: {
         NODE_ENV: 'production',
         PORT: 5000
       }
     }]
   };
   EOF
   
   # Start application
   pm2 start ecosystem.config.js
   pm2 save
   pm2 startup
   ```

4. **Configure Nginx (optional):**
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       # Serve static files
       location / {
           root /path/to/internal-messenger/client/build;
           try_files $uri $uri/ /index.html;
       }
       
       # Proxy API requests
       location /api {
           proxy_pass http://localhost:5000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
       }
       
       # WebSocket support
       location /socket.io/ {
           proxy_pass http://localhost:5000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection "upgrade";
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }
   }
   ```

### Option 2: Docker Deployment

1. **Create Dockerfile for server:**
   ```dockerfile
   # server/Dockerfile
   FROM node:18-alpine
   
   WORKDIR /app
   
   COPY package*.json ./
   RUN npm ci --only=production
   
   COPY . .
   
   EXPOSE 5000
   
   CMD ["node", "index.js"]
   ```

2. **Create Dockerfile for client:**
   ```dockerfile
   # client/Dockerfile
   FROM node:18-alpine as build
   
   WORKDIR /app
   
   COPY package*.json ./
   RUN npm ci
   
   COPY . .
   RUN npm run build
   
   FROM nginx:alpine
   COPY --from=build /app/build /usr/share/nginx/html
   COPY nginx.conf /etc/nginx/nginx.conf
   
   EXPOSE 80
   
   CMD ["nginx", "-g", "daemon off;"]
   ```

3. **Create docker-compose.yml:**
   ```yaml
   version: '3.8'
   
   services:
     mongodb:
       image: mongo:6.0
       container_name: internal-messenger-db
       restart: unless-stopped
       environment:
         MONGO_INITDB_ROOT_USERNAME: admin
         MONGO_INITDB_ROOT_PASSWORD: password
       volumes:
         - mongodb_data:/data/db
       ports:
         - "27017:27017"
   
     server:
       build: ./server
       container_name: internal-messenger-server
       restart: unless-stopped
       environment:
         NODE_ENV: production
         MONGODB_URI: **************************************************************************
         JWT_SECRET: your-production-jwt-secret
         CLIENT_URL: http://localhost:3000
       ports:
         - "5000:5000"
       depends_on:
         - mongodb
       volumes:
         - ./server/uploads:/app/uploads
   
     client:
       build: ./client
       container_name: internal-messenger-client
       restart: unless-stopped
       ports:
         - "3000:80"
       depends_on:
         - server
   
   volumes:
     mongodb_data:
   ```

4. **Deploy with Docker:**
   ```bash
   # Build and start
   docker-compose up -d
   
   # View logs
   docker-compose logs -f
   
   # Stop
   docker-compose down
   ```

### Option 3: Cloud Deployment (Heroku)

1. **Prepare for Heroku:**
   ```bash
   # Install Heroku CLI
   npm install -g heroku
   
   # Login to Heroku
   heroku login
   
   # Create app
   heroku create your-app-name
   ```

2. **Configure buildpacks:**
   ```bash
   heroku buildpacks:add heroku/nodejs
   heroku buildpacks:add https://github.com/heroku/heroku-buildpack-static
   ```

3. **Set environment variables:**
   ```bash
   heroku config:set NODE_ENV=production
   heroku config:set JWT_SECRET=your-production-jwt-secret
   heroku config:set CLIENT_URL=https://your-app-name.herokuapp.com
   ```

4. **Add MongoDB:**
   ```bash
   heroku addons:create mongolab:sandbox
   ```

5. **Deploy:**
   ```bash
   git add .
   git commit -m "Deploy to Heroku"
   git push heroku main
   ```

## Security Considerations

1. **Environment Variables:**
   - Never commit `.env` files to version control
   - Use strong, unique JWT secrets
   - Rotate secrets regularly

2. **Database Security:**
   - Use MongoDB authentication
   - Enable SSL/TLS for database connections
   - Regular backups

3. **Application Security:**
   - Keep dependencies updated
   - Use HTTPS in production
   - Implement rate limiting
   - Validate all inputs

4. **File Upload Security:**
   - Limit file sizes
   - Validate file types
   - Scan for malware
   - Store files outside web root

## Monitoring and Maintenance

1. **Logging:**
   ```bash
   # PM2 logs
   pm2 logs
   
   # Docker logs
   docker-compose logs -f
   ```

2. **Health Checks:**
   - Monitor `/api/health` endpoint
   - Set up uptime monitoring
   - Monitor database connections

3. **Backups:**
   ```bash
   # MongoDB backup
   mongodump --uri="mongodb://localhost:27017/internal-messenger" --out=/backup/$(date +%Y%m%d)
   ```

4. **Updates:**
   ```bash
   # Update dependencies
   npm audit fix
   
   # Update application
   git pull origin main
   npm run install-all
   pm2 restart all
   ```

## Troubleshooting

### Common Issues

1. **Connection refused:**
   - Check if MongoDB is running
   - Verify connection string
   - Check firewall settings

2. **CORS errors:**
   - Verify CLIENT_URL environment variable
   - Check CORS configuration

3. **Socket.io connection issues:**
   - Check WebSocket support
   - Verify proxy configuration
   - Check firewall/load balancer settings

4. **File upload issues:**
   - Check upload directory permissions
   - Verify file size limits
   - Check disk space

### Performance Optimization

1. **Database:**
   - Add appropriate indexes
   - Use connection pooling
   - Monitor query performance

2. **Application:**
   - Enable gzip compression
   - Use CDN for static assets
   - Implement caching

3. **Frontend:**
   - Code splitting
   - Lazy loading
   - Image optimization
