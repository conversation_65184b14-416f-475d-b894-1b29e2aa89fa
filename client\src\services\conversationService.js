import api from './api';

const conversationService = {
  // Get all conversations for the current user
  getConversations: async (page = 1, limit = 20) => {
    const response = await api.get('/conversations', {
      params: { page, limit }
    });
    return response;
  },

  // Get a specific conversation by ID
  getConversation: async (conversationId) => {
    const response = await api.get(`/conversations/${conversationId}`);
    return response;
  },

  // Create a new conversation
  createConversation: async (conversationData) => {
    const response = await api.post('/conversations', conversationData);
    return response;
  },

  // Update conversation details
  updateConversation: async (conversationId, updateData) => {
    const response = await api.put(`/conversations/${conversationId}`, updateData);
    return response;
  },

  // Add participant to conversation
  addParticipant: async (conversationId, userId) => {
    const response = await api.post(`/conversations/${conversationId}/participants`, {
      userId
    });
    return response;
  },

  // Remove participant from conversation
  removeParticipant: async (conversationId, userId) => {
    const response = await api.delete(`/conversations/${conversationId}/participants/${userId}`);
    return response;
  },

  // Leave conversation (remove self)
  leaveConversation: async (conversationId, userId) => {
    const response = await api.delete(`/conversations/${conversationId}/participants/${userId}`);
    return response;
  },

  // Search conversations
  searchConversations: async (query) => {
    const response = await api.get('/conversations/search', {
      params: { q: query }
    });
    return response;
  },
};

export default conversationService;
