import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { io } from 'socket.io-client';

export const initializeSocket = createAsyncThunk(
  'socket/initialize',
  async (_, { getState, dispatch }) => {
    const { auth } = getState();
    
    if (!auth.token) {
      throw new Error('No authentication token');
    }

    const socket = io(process.env.REACT_APP_SERVER_URL || 'http://localhost:5000', {
      auth: {
        token: auth.token
      },
      autoConnect: true,
    });

    // Set up event listeners
    socket.on('connect', () => {
      console.log('🔌 Connected to server');
      dispatch(setConnected(true));
    });

    socket.on('disconnect', () => {
      console.log('🔌 Disconnected from server');
      dispatch(setConnected(false));
    });

    socket.on('connect_error', (error) => {
      console.error('🔌 Connection error:', error);
      dispatch(setError(error.message));
    });

    // Message events
    socket.on('new_message', (data) => {
      dispatch(receiveMessage(data));
    });

    socket.on('message_read', (data) => {
      dispatch(messageRead(data));
    });

    // Typing events
    socket.on('user_typing', (data) => {
      dispatch(userTyping(data));
    });

    // User status events
    socket.on('user_status_change', (data) => {
      dispatch(userStatusChange(data));
    });

    // Conversation events
    socket.on('user_joined_conversation', (data) => {
      dispatch(userJoinedConversation(data));
    });

    socket.on('user_left_conversation', (data) => {
      dispatch(userLeftConversation(data));
    });

    return socket;
  }
);

const initialState = {
  instance: null,
  connected: false,
  error: null,
  typingUsers: {}, // { conversationId: [userId1, userId2] }
  onlineUsers: new Set(),
};

const socketSlice = createSlice({
  name: 'socket',
  initialState,
  reducers: {
    setSocket: (state, action) => {
      state.instance = action.payload;
    },
    setConnected: (state, action) => {
      state.connected = action.payload;
      if (action.payload) {
        state.error = null;
      }
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    disconnect: (state) => {
      if (state.instance) {
        state.instance.disconnect();
        state.instance = null;
      }
      state.connected = false;
      state.typingUsers = {};
      state.onlineUsers = new Set();
    },
    
    // Message events
    receiveMessage: (state, action) => {
      // This will be handled by messagesSlice
    },
    messageRead: (state, action) => {
      // This will be handled by messagesSlice
    },
    
    // Typing events
    userTyping: (state, action) => {
      const { conversationId, userId, isTyping } = action.payload;
      
      if (!state.typingUsers[conversationId]) {
        state.typingUsers[conversationId] = [];
      }
      
      if (isTyping) {
        if (!state.typingUsers[conversationId].includes(userId)) {
          state.typingUsers[conversationId].push(userId);
        }
      } else {
        state.typingUsers[conversationId] = state.typingUsers[conversationId].filter(
          id => id !== userId
        );
        
        if (state.typingUsers[conversationId].length === 0) {
          delete state.typingUsers[conversationId];
        }
      }
    },
    
    // User status events
    userStatusChange: (state, action) => {
      const { userId, status } = action.payload;
      
      if (status === 'online') {
        state.onlineUsers.add(userId);
      } else {
        state.onlineUsers.delete(userId);
      }
    },
    
    // Conversation events
    userJoinedConversation: (state, action) => {
      // This can be handled by conversationsSlice if needed
    },
    userLeftConversation: (state, action) => {
      // This can be handled by conversationsSlice if needed
    },
    
    // Socket actions
    joinConversation: (state, action) => {
      if (state.instance && state.connected) {
        state.instance.emit('join_conversation', action.payload);
      }
    },
    leaveConversation: (state, action) => {
      if (state.instance && state.connected) {
        state.instance.emit('leave_conversation', action.payload);
      }
    },
    sendMessage: (state, action) => {
      if (state.instance && state.connected) {
        state.instance.emit('send_message', action.payload);
      }
    },
    startTyping: (state, action) => {
      if (state.instance && state.connected) {
        state.instance.emit('typing_start', action.payload);
      }
    },
    stopTyping: (state, action) => {
      if (state.instance && state.connected) {
        state.instance.emit('typing_stop', action.payload);
      }
    },
    markMessageRead: (state, action) => {
      if (state.instance && state.connected) {
        state.instance.emit('mark_message_read', action.payload);
      }
    },
    changeStatus: (state, action) => {
      if (state.instance && state.connected) {
        state.instance.emit('status_change', action.payload);
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(initializeSocket.fulfilled, (state, action) => {
        state.instance = action.payload;
        state.error = null;
      })
      .addCase(initializeSocket.rejected, (state, action) => {
        state.error = action.error.message;
        state.connected = false;
      });
  },
});

export const {
  setSocket,
  setConnected,
  setError,
  clearError,
  disconnect,
  receiveMessage,
  messageRead,
  userTyping,
  userStatusChange,
  userJoinedConversation,
  userLeftConversation,
  joinConversation,
  leaveConversation,
  sendMessage,
  startTyping,
  stopTyping,
  markMessageRead,
  changeStatus,
} = socketSlice.actions;

export default socketSlice.reducer;
