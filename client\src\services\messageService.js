import api from './api';

const messageService = {
  // Get messages for a conversation
  getMessages: async (conversationId, page = 1, limit = 50) => {
    const response = await api.get(`/messages/${conversationId}`, {
      params: { page, limit }
    });
    return response;
  },

  // Send a text message
  sendMessage: async (messageData) => {
    const response = await api.post('/messages', messageData);
    return response;
  },

  // Send a file message
  sendFileMessage: async (formData) => {
    const response = await api.post('/messages/file', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response;
  },

  // Edit a message
  editMessage: async (messageId, content) => {
    const response = await api.put(`/messages/${messageId}`, { content });
    return response;
  },

  // Delete a message
  deleteMessage: async (messageId) => {
    const response = await api.delete(`/messages/${messageId}`);
    return response;
  },

  // Add reaction to message
  addReaction: async (messageId, emoji) => {
    const response = await api.post(`/messages/${messageId}/react`, { emoji });
    return response;
  },

  // Remove reaction from message
  removeReaction: async (messageId, emoji) => {
    const response = await api.delete(`/messages/${messageId}/react`, {
      data: { emoji }
    });
    return response;
  },

  // Mark message as read
  markAsRead: async (messageId, conversationId) => {
    const response = await api.put(`/messages/${messageId}/read`, {
      conversationId
    });
    return response;
  },

  // Search messages in a conversation
  searchMessages: async (conversationId, query) => {
    const response = await api.get(`/messages/${conversationId}/search`, {
      params: { q: query }
    });
    return response;
  },
};

export default messageService;
