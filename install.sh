#!/bin/bash

echo "========================================"
echo "Internal Messenger Installation Script"
echo "========================================"
echo

echo "Installing dependencies..."
echo

echo "[1/3] Installing root dependencies..."
npm install
if [ $? -ne 0 ]; then
    echo "Error: Failed to install root dependencies"
    exit 1
fi

echo
echo "[2/3] Installing server dependencies..."
cd server
npm install
if [ $? -ne 0 ]; then
    echo "Error: Failed to install server dependencies"
    exit 1
fi

echo
echo "[3/3] Installing client dependencies..."
cd ../client
npm install
if [ $? -ne 0 ]; then
    echo "Error: Failed to install client dependencies"
    exit 1
fi

cd ..

echo
echo "========================================"
echo "Installation completed successfully!"
echo "========================================"
echo
echo "Next steps:"
echo "1. Copy server/.env.example to server/.env"
echo "2. Configure your environment variables"
echo "3. Make sure MongoDB is running"
echo "4. Run 'npm run dev' to start the application"
echo
