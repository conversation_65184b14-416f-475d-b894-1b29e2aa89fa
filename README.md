# Internal Messenger

A WhatsApp-like messenger application for internal communication built with modern open source technologies.

## Features

- 🔐 **Secure Authentication** - JWT-based user authentication
- 💬 **Real-time Messaging** - Instant messaging with Socket.io
- 👥 **Group Chats** - Create and manage group conversations
- 📁 **File Sharing** - Share images, documents, and files
- 🟢 **Online Status** - See who's online and typing indicators
- 📱 **Responsive Design** - Works on desktop and mobile devices
- 🔔 **Message Notifications** - Real-time message delivery status

## Technology Stack

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **Socket.io** - Real-time communication
- **MongoDB** - Database
- **Mongoose** - MongoDB ODM
- **JWT** - Authentication
- **Multer** - File upload handling
- **bcryptjs** - Password hashing

### Frontend
- **React.js** - UI framework
- **Material-UI** - Component library
- **Redux Toolkit** - State management
- **Socket.io-client** - Real-time client
- **Axios** - HTTP client

## Project Structure

```
internal-messenger/
├── server/                 # Backend application
│   ├── controllers/        # Route controllers
│   ├── middleware/         # Custom middleware
│   ├── models/            # Database models
│   ├── routes/            # API routes
│   ├── socket/            # Socket.io handlers
│   ├── uploads/           # File uploads directory
│   └── index.js           # Server entry point
├── client/                # Frontend application
│   ├── public/            # Static files
│   ├── src/               # React source code
│   │   ├── components/    # React components
│   │   ├── pages/         # Page components
│   │   ├── store/         # Redux store
│   │   ├── services/      # API services
│   │   └── utils/         # Utility functions
│   └── package.json       # Client dependencies
└── package.json           # Root package.json
```

## Quick Start

1. **Install dependencies**
   ```bash
   npm run install-all
   ```

2. **Set up environment variables**
   ```bash
   # Create server/.env file with:
   MONGODB_URI=mongodb://localhost:27017/internal-messenger
   JWT_SECRET=your-secret-key
   PORT=5000
   ```

3. **Start the application**
   ```bash
   npm run dev
   ```

4. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000

## Environment Setup

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local or cloud instance)
- npm or yarn

### Installation Steps
1. Clone the repository
2. Install dependencies: `npm run install-all`
3. Configure environment variables
4. Start MongoDB service
5. Run the application: `npm run dev`

## API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user

### Messages
- `GET /api/messages/:conversationId` - Get conversation messages
- `POST /api/messages` - Send a message
- `PUT /api/messages/:id` - Update message status

### Conversations
- `GET /api/conversations` - Get user conversations
- `POST /api/conversations` - Create new conversation
- `PUT /api/conversations/:id` - Update conversation

### Users
- `GET /api/users` - Get all users
- `GET /api/users/search` - Search users
- `PUT /api/users/profile` - Update user profile

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details
