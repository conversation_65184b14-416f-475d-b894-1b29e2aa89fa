const jwt = require('jsonwebtoken');
const User = require('../models/User');
const Message = require('../models/Message');
const Conversation = require('../models/Conversation');

// Store active connections
const activeUsers = new Map();
const typingUsers = new Map();

const socketHandler = (io) => {
  // Authentication middleware for socket connections
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      
      if (!token) {
        return next(new Error('Authentication error: No token provided'));
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findById(decoded.userId).select('-password');
      
      if (!user || !user.isActive) {
        return next(new Error('Authentication error: Invalid user'));
      }

      socket.userId = user._id.toString();
      socket.user = user;
      next();
    } catch (error) {
      next(new Error('Authentication error: Invalid token'));
    }
  });

  io.on('connection', async (socket) => {
    console.log(`🔌 User ${socket.user.username} connected (${socket.id})`);

    // Store active user
    activeUsers.set(socket.userId, {
      socketId: socket.id,
      user: socket.user,
      lastSeen: new Date()
    });

    // Update user status to online
    await User.findByIdAndUpdate(socket.userId, {
      status: 'online',
      lastSeen: new Date()
    });

    // Join user to their conversation rooms
    try {
      const userConversations = await Conversation.find({
        'participants.user': socket.userId,
        isActive: true
      });

      userConversations.forEach(conv => {
        socket.join(conv._id.toString());
      });

      console.log(`📱 User ${socket.user.username} joined ${userConversations.length} conversation rooms`);
    } catch (error) {
      console.error('Error joining conversation rooms:', error);
    }

    // Broadcast user online status to their contacts
    socket.broadcast.emit('user_status_change', {
      userId: socket.userId,
      status: 'online',
      lastSeen: new Date()
    });

    // Handle joining a conversation room
    socket.on('join_conversation', async (conversationId) => {
      try {
        const conversation = await Conversation.findById(conversationId);
        
        if (conversation && conversation.isParticipant(socket.userId)) {
          socket.join(conversationId);
          console.log(`👥 User ${socket.user.username} joined conversation ${conversationId}`);
          
          // Update last read timestamp
          await conversation.updateLastRead(socket.userId);
          
          // Notify other participants
          socket.to(conversationId).emit('user_joined_conversation', {
            userId: socket.userId,
            username: socket.user.username,
            conversationId
          });
        }
      } catch (error) {
        console.error('Error joining conversation:', error);
        socket.emit('error', { message: 'Failed to join conversation' });
      }
    });

    // Handle leaving a conversation room
    socket.on('leave_conversation', (conversationId) => {
      socket.leave(conversationId);
      console.log(`👋 User ${socket.user.username} left conversation ${conversationId}`);
      
      // Notify other participants
      socket.to(conversationId).emit('user_left_conversation', {
        userId: socket.userId,
        username: socket.user.username,
        conversationId
      });
    });

    // Handle sending messages
    socket.on('send_message', async (data) => {
      try {
        const { conversationId, content, type = 'text', replyTo } = data;

        // Validate conversation and user participation
        const conversation = await Conversation.findById(conversationId);
        
        if (!conversation || !conversation.isParticipant(socket.userId)) {
          return socket.emit('error', { message: 'Invalid conversation or access denied' });
        }

        // Create and save message
        const message = new Message({
          conversation: conversationId,
          sender: socket.userId,
          content,
          type,
          replyTo: replyTo || undefined
        });

        await message.save();

        // Update conversation
        conversation.lastMessage = message._id;
        await conversation.updateLastActivity();

        // Populate message for broadcasting
        await message.populate('sender', 'username firstName lastName avatar');
        if (replyTo) {
          await message.populate('replyTo', 'content sender type');
          await message.populate('replyTo.sender', 'username firstName lastName');
        }

        // Broadcast message to conversation participants
        io.to(conversationId).emit('new_message', {
          message,
          conversationId
        });

        // Mark message as delivered to online participants
        const onlineParticipants = conversation.participants.filter(p => 
          activeUsers.has(p.user.toString()) && p.user.toString() !== socket.userId
        );

        for (const participant of onlineParticipants) {
          await message.markAsDelivered(participant.user);
        }

        console.log(`💬 Message sent in conversation ${conversationId} by ${socket.user.username}`);
      } catch (error) {
        console.error('Error sending message:', error);
        socket.emit('error', { message: 'Failed to send message' });
      }
    });

    // Handle typing indicators
    socket.on('typing_start', (data) => {
      const { conversationId } = data;
      
      if (!typingUsers.has(conversationId)) {
        typingUsers.set(conversationId, new Set());
      }
      
      typingUsers.get(conversationId).add(socket.userId);
      
      socket.to(conversationId).emit('user_typing', {
        userId: socket.userId,
        username: socket.user.username,
        conversationId,
        isTyping: true
      });

      console.log(`⌨️ User ${socket.user.username} started typing in ${conversationId}`);
    });

    socket.on('typing_stop', (data) => {
      const { conversationId } = data;
      
      if (typingUsers.has(conversationId)) {
        typingUsers.get(conversationId).delete(socket.userId);
        
        if (typingUsers.get(conversationId).size === 0) {
          typingUsers.delete(conversationId);
        }
      }
      
      socket.to(conversationId).emit('user_typing', {
        userId: socket.userId,
        username: socket.user.username,
        conversationId,
        isTyping: false
      });

      console.log(`⌨️ User ${socket.user.username} stopped typing in ${conversationId}`);
    });

    // Handle message read receipts
    socket.on('mark_message_read', async (data) => {
      try {
        const { messageId, conversationId } = data;
        
        const message = await Message.findById(messageId);
        if (message && message.conversation.toString() === conversationId) {
          await message.markAsRead(socket.userId);
          
          // Notify sender about read receipt
          socket.to(conversationId).emit('message_read', {
            messageId,
            userId: socket.userId,
            readAt: new Date()
          });
        }
      } catch (error) {
        console.error('Error marking message as read:', error);
      }
    });

    // Handle status changes
    socket.on('status_change', async (data) => {
      try {
        const { status } = data;
        
        if (['online', 'away', 'offline'].includes(status)) {
          await User.findByIdAndUpdate(socket.userId, {
            status,
            lastSeen: new Date()
          });

          // Update active users map
          if (activeUsers.has(socket.userId)) {
            activeUsers.get(socket.userId).lastSeen = new Date();
          }

          // Broadcast status change
          socket.broadcast.emit('user_status_change', {
            userId: socket.userId,
            status,
            lastSeen: new Date()
          });

          console.log(`📊 User ${socket.user.username} status changed to ${status}`);
        }
      } catch (error) {
        console.error('Error updating status:', error);
      }
    });

    // Handle disconnection
    socket.on('disconnect', async () => {
      console.log(`🔌 User ${socket.user.username} disconnected (${socket.id})`);

      // Remove from active users
      activeUsers.delete(socket.userId);

      // Clean up typing indicators
      for (const [conversationId, typingSet] of typingUsers.entries()) {
        if (typingSet.has(socket.userId)) {
          typingSet.delete(socket.userId);
          
          socket.to(conversationId).emit('user_typing', {
            userId: socket.userId,
            username: socket.user.username,
            conversationId,
            isTyping: false
          });
          
          if (typingSet.size === 0) {
            typingUsers.delete(conversationId);
          }
        }
      }

      // Update user status to offline
      try {
        await User.findByIdAndUpdate(socket.userId, {
          status: 'offline',
          lastSeen: new Date()
        });

        // Broadcast offline status
        socket.broadcast.emit('user_status_change', {
          userId: socket.userId,
          status: 'offline',
          lastSeen: new Date()
        });
      } catch (error) {
        console.error('Error updating user status on disconnect:', error);
      }
    });

    // Handle errors
    socket.on('error', (error) => {
      console.error(`Socket error for user ${socket.user.username}:`, error);
    });
  });

  // Cleanup inactive connections periodically
  setInterval(() => {
    const now = new Date();
    for (const [userId, userData] of activeUsers.entries()) {
      const timeSinceLastSeen = now - userData.lastSeen;
      
      // Remove users inactive for more than 5 minutes
      if (timeSinceLastSeen > 5 * 60 * 1000) {
        activeUsers.delete(userId);
        console.log(`🧹 Cleaned up inactive user: ${userData.user.username}`);
      }
    }
  }, 60000); // Run every minute
};

module.exports = socketHandler;
