import api from './api';

const userService = {
  // Get all users
  getUsers: async (page = 1, limit = 20, search = '') => {
    const response = await api.get('/users', {
      params: { page, limit, search }
    });
    return response;
  },

  // Search users
  searchUsers: async (query) => {
    const response = await api.get('/users/search', {
      params: { q: query }
    });
    return response;
  },

  // Get user by ID
  getUser: async (userId) => {
    const response = await api.get(`/users/${userId}`);
    return response;
  },

  // Update user profile
  updateProfile: async (profileData) => {
    const response = await api.put('/users/profile', profileData);
    return response;
  },

  // Upload user avatar
  uploadAvatar: async (formData) => {
    const response = await api.post('/users/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response;
  },

  // Delete user avatar
  deleteAvatar: async () => {
    const response = await api.delete('/users/avatar');
    return response;
  },

  // Get user's online status
  getUserStatus: async (userId) => {
    const response = await api.get(`/users/${userId}/status`);
    return response;
  },

  // Update user's status
  updateUserStatus: async (status) => {
    const response = await api.put('/users/status', { status });
    return response;
  },
};

export default userService;
