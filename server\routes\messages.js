const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const Message = require('../models/Message');
const Conversation = require('../models/Conversation');
const { auth } = require('../middleware/auth');
const { validateMessage } = require('../middleware/validation');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(__dirname, '../uploads/files');
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const sanitizedName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, '_');
    cb(null, `${uniqueSuffix}-${sanitizedName}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024 // 10MB default
  },
  fileFilter: (req, file, cb) => {
    // Allow most common file types
    const allowedTypes = /jpeg|jpg|png|gif|pdf|doc|docx|txt|zip|mp4|mp3|wav/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    
    if (extname) {
      return cb(null, true);
    } else {
      cb(new Error('File type not allowed'));
    }
  }
});

// @route   GET /api/messages/:conversationId
// @desc    Get messages for a conversation
// @access  Private
router.get('/:conversationId', auth, async (req, res) => {
  try {
    const { page = 1, limit = 50 } = req.query;
    const skip = (page - 1) * limit;

    // Check if conversation exists and user is participant
    const conversation = await Conversation.findById(req.params.conversationId);
    
    if (!conversation) {
      return res.status(404).json({
        message: 'Conversation not found'
      });
    }

    if (!conversation.isParticipant(req.user._id)) {
      return res.status(403).json({
        message: 'Access denied. You are not a participant in this conversation.'
      });
    }

    const messages = await Message.find({
      conversation: req.params.conversationId,
      isDeleted: false
    })
    .populate('sender', 'username firstName lastName avatar')
    .populate('replyTo', 'content sender type')
    .populate('replyTo.sender', 'username firstName lastName')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit));

    const total = await Message.countDocuments({
      conversation: req.params.conversationId,
      isDeleted: false
    });

    // Update last read timestamp for the user
    await conversation.updateLastRead(req.user._id);

    res.json({
      messages: messages.reverse(), // Reverse to show oldest first
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('Get messages error:', error);
    res.status(500).json({
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/messages
// @desc    Send a message
// @access  Private
router.post('/', auth, validateMessage, async (req, res) => {
  try {
    const { conversationId, content, type = 'text', replyTo } = req.body;

    // Check if conversation exists and user is participant
    const conversation = await Conversation.findById(conversationId);
    
    if (!conversation) {
      return res.status(404).json({
        message: 'Conversation not found'
      });
    }

    if (!conversation.isParticipant(req.user._id)) {
      return res.status(403).json({
        message: 'Access denied. You are not a participant in this conversation.'
      });
    }

    // Validate reply message if provided
    if (replyTo) {
      const replyMessage = await Message.findById(replyTo);
      if (!replyMessage || replyMessage.conversation.toString() !== conversationId) {
        return res.status(400).json({
          message: 'Invalid reply message'
        });
      }
    }

    // Create message
    const message = new Message({
      conversation: conversationId,
      sender: req.user._id,
      content,
      type,
      replyTo: replyTo || undefined
    });

    await message.save();

    // Update conversation's last message and activity
    conversation.lastMessage = message._id;
    await conversation.updateLastActivity();

    // Populate message for response
    await message.populate('sender', 'username firstName lastName avatar');
    if (replyTo) {
      await message.populate('replyTo', 'content sender type');
      await message.populate('replyTo.sender', 'username firstName lastName');
    }

    res.status(201).json({
      message: 'Message sent successfully',
      data: message
    });
  } catch (error) {
    console.error('Send message error:', error);
    res.status(500).json({
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/messages/file
// @desc    Send a file message
// @access  Private
router.post('/file', auth, upload.single('file'), async (req, res) => {
  try {
    const { conversationId, replyTo } = req.body;

    if (!req.file) {
      return res.status(400).json({
        message: 'No file uploaded'
      });
    }

    // Check if conversation exists and user is participant
    const conversation = await Conversation.findById(conversationId);
    
    if (!conversation) {
      return res.status(404).json({
        message: 'Conversation not found'
      });
    }

    if (!conversation.isParticipant(req.user._id)) {
      return res.status(403).json({
        message: 'Access denied. You are not a participant in this conversation.'
      });
    }

    // Check if file sharing is allowed
    if (!conversation.settings.allowFileSharing) {
      return res.status(403).json({
        message: 'File sharing is not allowed in this conversation'
      });
    }

    // Determine message type based on file
    let messageType = 'file';
    if (req.file.mimetype.startsWith('image/')) {
      messageType = 'image';
    } else if (req.file.mimetype.startsWith('video/')) {
      messageType = 'video';
    } else if (req.file.mimetype.startsWith('audio/')) {
      messageType = 'audio';
    }

    // Create message with file
    const message = new Message({
      conversation: conversationId,
      sender: req.user._id,
      content: req.file.originalname,
      type: messageType,
      file: {
        filename: req.file.filename,
        originalName: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size,
        path: req.file.path,
        url: `/uploads/files/${req.file.filename}`
      },
      replyTo: replyTo || undefined
    });

    await message.save();

    // Update conversation's last message and activity
    conversation.lastMessage = message._id;
    await conversation.updateLastActivity();

    // Populate message for response
    await message.populate('sender', 'username firstName lastName avatar');
    if (replyTo) {
      await message.populate('replyTo', 'content sender type');
      await message.populate('replyTo.sender', 'username firstName lastName');
    }

    res.status(201).json({
      message: 'File sent successfully',
      data: message
    });
  } catch (error) {
    console.error('Send file error:', error);
    res.status(500).json({
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   PUT /api/messages/:id
// @desc    Edit a message
// @access  Private
router.put('/:id', auth, async (req, res) => {
  try {
    const { content } = req.body;

    if (!content || content.trim().length === 0) {
      return res.status(400).json({
        message: 'Message content is required'
      });
    }

    const message = await Message.findById(req.params.id);

    if (!message) {
      return res.status(404).json({
        message: 'Message not found'
      });
    }

    // Check if user is the sender
    if (message.sender.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        message: 'Access denied. You can only edit your own messages.'
      });
    }

    // Check if message is not too old (e.g., 24 hours)
    const hoursSinceCreated = (Date.now() - message.createdAt) / (1000 * 60 * 60);
    if (hoursSinceCreated > 24) {
      return res.status(400).json({
        message: 'Cannot edit messages older than 24 hours'
      });
    }

    await message.editContent(content.trim());
    await message.populate('sender', 'username firstName lastName avatar');

    res.json({
      message: 'Message updated successfully',
      data: message
    });
  } catch (error) {
    console.error('Edit message error:', error);
    res.status(500).json({
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   DELETE /api/messages/:id
// @desc    Delete a message
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const message = await Message.findById(req.params.id);

    if (!message) {
      return res.status(404).json({
        message: 'Message not found'
      });
    }

    // Check if user is the sender
    if (message.sender.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        message: 'Access denied. You can only delete your own messages.'
      });
    }

    await message.softDelete(req.user._id);

    res.json({
      message: 'Message deleted successfully'
    });
  } catch (error) {
    console.error('Delete message error:', error);
    res.status(500).json({
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/messages/:id/react
// @desc    Add reaction to message
// @access  Private
router.post('/:id/react', auth, async (req, res) => {
  try {
    const { emoji } = req.body;

    if (!emoji) {
      return res.status(400).json({
        message: 'Emoji is required'
      });
    }

    const message = await Message.findById(req.params.id);

    if (!message) {
      return res.status(404).json({
        message: 'Message not found'
      });
    }

    // Check if user is participant in the conversation
    const conversation = await Conversation.findById(message.conversation);
    if (!conversation.isParticipant(req.user._id)) {
      return res.status(403).json({
        message: 'Access denied. You are not a participant in this conversation.'
      });
    }

    await message.addReaction(req.user._id, emoji);

    res.json({
      message: 'Reaction added successfully',
      reactions: message.reactions
    });
  } catch (error) {
    console.error('Add reaction error:', error);
    res.status(500).json({
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
