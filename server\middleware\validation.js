const Joi = require('joi');

// User registration validation
const validateRegistration = (req, res, next) => {
  const schema = Joi.object({
    username: Joi.string()
      .alphanum()
      .min(3)
      .max(30)
      .required()
      .messages({
        'string.alphanum': 'Username must contain only alphanumeric characters',
        'string.min': 'Username must be at least 3 characters long',
        'string.max': 'Username cannot exceed 30 characters',
        'any.required': 'Username is required'
      }),
    email: Joi.string()
      .email()
      .required()
      .messages({
        'string.email': 'Please provide a valid email address',
        'any.required': 'Email is required'
      }),
    password: Joi.string()
      .min(6)
      .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)'))
      .required()
      .messages({
        'string.min': 'Password must be at least 6 characters long',
        'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
        'any.required': 'Password is required'
      }),
    firstName: Joi.string()
      .trim()
      .min(1)
      .max(50)
      .required()
      .messages({
        'string.min': 'First name is required',
        'string.max': 'First name cannot exceed 50 characters',
        'any.required': 'First name is required'
      }),
    lastName: Joi.string()
      .trim()
      .min(1)
      .max(50)
      .required()
      .messages({
        'string.min': 'Last name is required',
        'string.max': 'Last name cannot exceed 50 characters',
        'any.required': 'Last name is required'
      }),
    department: Joi.string()
      .trim()
      .max(100)
      .optional()
      .allow('')
      .messages({
        'string.max': 'Department cannot exceed 100 characters'
      }),
    position: Joi.string()
      .trim()
      .max(100)
      .optional()
      .allow('')
      .messages({
        'string.max': 'Position cannot exceed 100 characters'
      })
  });

  const { error } = schema.validate(req.body);
  if (error) {
    return res.status(400).json({
      message: 'Validation error',
      errors: error.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }))
    });
  }

  next();
};

// User login validation
const validateLogin = (req, res, next) => {
  const schema = Joi.object({
    email: Joi.string()
      .email()
      .required()
      .messages({
        'string.email': 'Please provide a valid email address',
        'any.required': 'Email is required'
      }),
    password: Joi.string()
      .required()
      .messages({
        'any.required': 'Password is required'
      })
  });

  const { error } = schema.validate(req.body);
  if (error) {
    return res.status(400).json({
      message: 'Validation error',
      errors: error.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }))
    });
  }

  next();
};

// Message validation
const validateMessage = (req, res, next) => {
  const schema = Joi.object({
    conversationId: Joi.string()
      .required()
      .messages({
        'any.required': 'Conversation ID is required'
      }),
    content: Joi.string()
      .trim()
      .max(5000)
      .when('type', {
        is: 'text',
        then: Joi.required(),
        otherwise: Joi.optional().allow('')
      })
      .messages({
        'string.max': 'Message content cannot exceed 5000 characters',
        'any.required': 'Message content is required for text messages'
      }),
    type: Joi.string()
      .valid('text', 'image', 'file', 'audio', 'video')
      .default('text')
      .messages({
        'any.only': 'Message type must be one of: text, image, file, audio, video'
      }),
    replyTo: Joi.string()
      .optional()
      .allow(null)
  });

  const { error } = schema.validate(req.body);
  if (error) {
    return res.status(400).json({
      message: 'Validation error',
      errors: error.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }))
    });
  }

  next();
};

// Conversation validation
const validateConversation = (req, res, next) => {
  const schema = Joi.object({
    name: Joi.string()
      .trim()
      .max(100)
      .when('type', {
        is: 'group',
        then: Joi.required(),
        otherwise: Joi.optional().allow('')
      })
      .messages({
        'string.max': 'Conversation name cannot exceed 100 characters',
        'any.required': 'Conversation name is required for group chats'
      }),
    description: Joi.string()
      .trim()
      .max(500)
      .optional()
      .allow('')
      .messages({
        'string.max': 'Description cannot exceed 500 characters'
      }),
    type: Joi.string()
      .valid('direct', 'group')
      .default('direct')
      .messages({
        'any.only': 'Conversation type must be either direct or group'
      }),
    participants: Joi.array()
      .items(Joi.string())
      .min(1)
      .required()
      .messages({
        'array.min': 'At least one participant is required',
        'any.required': 'Participants are required'
      })
  });

  const { error } = schema.validate(req.body);
  if (error) {
    return res.status(400).json({
      message: 'Validation error',
      errors: error.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }))
    });
  }

  next();
};

module.exports = {
  validateRegistration,
  validateLogin,
  validateMessage,
  validateConversation
};
