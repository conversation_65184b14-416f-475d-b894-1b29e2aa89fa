import api from './api';

const authService = {
  // User registration
  register: async (userData) => {
    const response = await api.post('/auth/register', userData);
    return response;
  },

  // User login
  login: async (credentials) => {
    const response = await api.post('/auth/login', credentials);
    return response;
  },

  // User logout
  logout: async () => {
    const response = await api.post('/auth/logout');
    return response;
  },

  // Get current user
  getCurrentUser: async () => {
    const response = await api.get('/auth/me');
    return response;
  },

  // Update user status
  updateStatus: async (status) => {
    const response = await api.put('/auth/status', { status });
    return response;
  },

  // Check if token is valid
  validateToken: async () => {
    try {
      const response = await api.get('/auth/me');
      return { valid: true, user: response.user };
    } catch (error) {
      return { valid: false, error: error.message };
    }
  },
};

export default authService;
