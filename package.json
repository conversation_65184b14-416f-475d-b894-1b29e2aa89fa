{"name": "internal-messenger", "version": "1.0.0", "description": "WhatsApp-like messenger for internal communication", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm start", "build": "cd client && npm run build", "install-server": "cd server && npm install", "install-client": "cd client && npm install", "install-all": "npm run install-server && npm run install-client", "test": "cd server && npm test"}, "keywords": ["messenger", "chat", "real-time", "internal-communication"], "author": "Your Organization", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}