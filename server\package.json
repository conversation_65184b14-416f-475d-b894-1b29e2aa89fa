{"name": "internal-messenger-server", "version": "1.0.0", "description": "Backend server for internal messenger application", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.4", "mongoose": "^8.0.3", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "joi": "^17.11.0", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "keywords": ["express", "socket.io", "mongodb", "jwt", "messenger"], "author": "Your Organization", "license": "MIT"}