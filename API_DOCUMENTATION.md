# API Documentation

This document provides comprehensive documentation for the Internal Messenger API.

## Base URL

```
http://localhost:5000/api
```

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Response Format

All API responses follow this format:

```json
{
  "message": "Success message",
  "data": {}, // Response data (optional)
  "error": "Error message" // Only present on errors
}
```

## Error Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `429` - Too Many Requests
- `500` - Internal Server Error

## Endpoints

### Authentication

#### Register User
```http
POST /auth/register
```

**Request Body:**
```json
{
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "SecurePass123",
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "department": "Engineering",
  "position": "Software Developer"
}
```

**Response:**
```json
{
  "message": "User registered successfully",
  "token": "jwt-token-here",
  "user": {
    "id": "user-id",
    "username": "johndoe",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "fullName": "John Doe",
    "department": "Engineering",
    "position": "Software Developer",
    "avatar": null,
    "status": "offline"
  }
}
```

#### Login User
```http
POST /auth/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123"
}
```

**Response:**
```json
{
  "message": "Login successful",
  "token": "jwt-token-here",
  "user": {
    "id": "user-id",
    "username": "johndoe",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "fullName": "John Doe",
    "department": "Engineering",
    "position": "Software Developer",
    "avatar": null,
    "status": "online",
    "lastSeen": "2023-12-01T10:00:00.000Z"
  }
}
```

#### Get Current User
```http
GET /auth/me
```

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "user": {
    "id": "user-id",
    "username": "johndoe",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "fullName": "John Doe",
    "department": "Engineering",
    "position": "Software Developer",
    "avatar": "/uploads/avatars/avatar.jpg",
    "status": "online",
    "lastSeen": "2023-12-01T10:00:00.000Z"
  }
}
```

#### Logout User
```http
POST /auth/logout
```

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "message": "Logout successful"
}
```

#### Update Status
```http
PUT /auth/status
```

**Headers:**
```
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "status": "away"
}
```

**Response:**
```json
{
  "message": "Status updated successfully",
  "status": "away"
}
```

### Users

#### Get All Users
```http
GET /users?page=1&limit=20&search=john
```

**Headers:**
```
Authorization: Bearer <token>
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)
- `search` (optional): Search term

**Response:**
```json
{
  "users": [
    {
      "id": "user-id",
      "username": "johndoe",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "department": "Engineering",
      "position": "Software Developer",
      "avatar": "/uploads/avatars/avatar.jpg",
      "status": "online",
      "lastSeen": "2023-12-01T10:00:00.000Z"
    }
  ],
  "pagination": {
    "current": 1,
    "pages": 5,
    "total": 100
  }
}
```

#### Search Users
```http
GET /users/search?q=john
```

**Headers:**
```
Authorization: Bearer <token>
```

**Query Parameters:**
- `q`: Search query (minimum 2 characters)

**Response:**
```json
{
  "users": [
    {
      "id": "user-id",
      "username": "johndoe",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "department": "Engineering",
      "position": "Software Developer",
      "avatar": "/uploads/avatars/avatar.jpg",
      "status": "online"
    }
  ]
}
```

#### Get User by ID
```http
GET /users/:id
```

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "user": {
    "id": "user-id",
    "username": "johndoe",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "department": "Engineering",
    "position": "Software Developer",
    "avatar": "/uploads/avatars/avatar.jpg",
    "status": "online",
    "lastSeen": "2023-12-01T10:00:00.000Z"
  }
}
```

#### Update Profile
```http
PUT /users/profile
```

**Headers:**
```
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Smith",
  "department": "Engineering",
  "position": "Senior Developer"
}
```

**Response:**
```json
{
  "message": "Profile updated successfully",
  "user": {
    "id": "user-id",
    "username": "johndoe",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Smith",
    "fullName": "John Smith",
    "department": "Engineering",
    "position": "Senior Developer",
    "avatar": "/uploads/avatars/avatar.jpg",
    "status": "online"
  }
}
```

#### Upload Avatar
```http
POST /users/avatar
```

**Headers:**
```
Authorization: Bearer <token>
Content-Type: multipart/form-data
```

**Request Body:**
```
Form data with 'avatar' file field
```

**Response:**
```json
{
  "message": "Avatar uploaded successfully",
  "avatar": "/uploads/avatars/avatar-123456789.jpg"
}
```

### Conversations

#### Get Conversations
```http
GET /conversations?page=1&limit=20
```

**Headers:**
```
Authorization: Bearer <token>
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)

**Response:**
```json
{
  "conversations": [
    {
      "id": "conversation-id",
      "name": "Project Team",
      "description": "Discussion about the project",
      "type": "group",
      "participants": [
        {
          "user": {
            "id": "user-id",
            "username": "johndoe",
            "firstName": "John",
            "lastName": "Doe",
            "avatar": "/uploads/avatars/avatar.jpg",
            "status": "online"
          },
          "role": "admin",
          "joinedAt": "2023-12-01T09:00:00.000Z",
          "lastReadAt": "2023-12-01T10:00:00.000Z"
        }
      ],
      "createdBy": "user-id",
      "avatar": null,
      "lastMessage": {
        "id": "message-id",
        "content": "Hello everyone!",
        "type": "text",
        "sender": {
          "username": "johndoe",
          "firstName": "John",
          "lastName": "Doe"
        },
        "createdAt": "2023-12-01T10:00:00.000Z"
      },
      "lastActivity": "2023-12-01T10:00:00.000Z",
      "unreadCount": 3,
      "createdAt": "2023-12-01T09:00:00.000Z",
      "updatedAt": "2023-12-01T10:00:00.000Z"
    }
  ],
  "pagination": {
    "current": 1,
    "pages": 3,
    "total": 50
  }
}
```

#### Create Conversation
```http
POST /conversations
```

**Headers:**
```
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "name": "Project Team",
  "description": "Discussion about the project",
  "type": "group",
  "participants": ["user-id-1", "user-id-2"]
}
```

**Response:**
```json
{
  "message": "Conversation created successfully",
  "conversation": {
    "id": "conversation-id",
    "name": "Project Team",
    "description": "Discussion about the project",
    "type": "group",
    "participants": [
      {
        "user": {
          "id": "user-id",
          "username": "johndoe",
          "firstName": "John",
          "lastName": "Doe",
          "avatar": "/uploads/avatars/avatar.jpg",
          "status": "online"
        },
        "role": "admin",
        "joinedAt": "2023-12-01T10:00:00.000Z",
        "lastReadAt": "2023-12-01T10:00:00.000Z"
      }
    ],
    "createdBy": "user-id",
    "avatar": null,
    "lastMessage": null,
    "lastActivity": "2023-12-01T10:00:00.000Z",
    "createdAt": "2023-12-01T10:00:00.000Z",
    "updatedAt": "2023-12-01T10:00:00.000Z"
  }
}
```

#### Get Conversation
```http
GET /conversations/:id
```

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "conversation": {
    "id": "conversation-id",
    "name": "Project Team",
    "description": "Discussion about the project",
    "type": "group",
    "participants": [
      {
        "user": {
          "id": "user-id",
          "username": "johndoe",
          "firstName": "John",
          "lastName": "Doe",
          "avatar": "/uploads/avatars/avatar.jpg",
          "status": "online"
        },
        "role": "admin",
        "joinedAt": "2023-12-01T09:00:00.000Z",
        "lastReadAt": "2023-12-01T10:00:00.000Z"
      }
    ],
    "createdBy": "user-id",
    "avatar": null,
    "lastMessage": {
      "id": "message-id",
      "content": "Hello everyone!",
      "type": "text",
      "sender": {
        "username": "johndoe",
        "firstName": "John",
        "lastName": "Doe"
      },
      "createdAt": "2023-12-01T10:00:00.000Z"
    },
    "lastActivity": "2023-12-01T10:00:00.000Z",
    "createdAt": "2023-12-01T09:00:00.000Z",
    "updatedAt": "2023-12-01T10:00:00.000Z"
  }
}
```

### Messages

#### Get Messages
```http
GET /messages/:conversationId?page=1&limit=50
```

**Headers:**
```
Authorization: Bearer <token>
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 50)

**Response:**
```json
{
  "messages": [
    {
      "id": "message-id",
      "conversation": "conversation-id",
      "sender": {
        "id": "user-id",
        "username": "johndoe",
        "firstName": "John",
        "lastName": "Doe",
        "avatar": "/uploads/avatars/avatar.jpg"
      },
      "content": "Hello everyone!",
      "type": "text",
      "file": null,
      "replyTo": null,
      "edited": {
        "isEdited": false,
        "editedAt": null,
        "originalContent": null
      },
      "reactions": [
        {
          "user": "user-id",
          "emoji": "👍",
          "createdAt": "2023-12-01T10:01:00.000Z"
        }
      ],
      "readBy": [
        {
          "user": "user-id",
          "readAt": "2023-12-01T10:01:00.000Z"
        }
      ],
      "deliveredTo": [
        {
          "user": "user-id",
          "deliveredAt": "2023-12-01T10:00:30.000Z"
        }
      ],
      "status": "read",
      "isDeleted": false,
      "createdAt": "2023-12-01T10:00:00.000Z",
      "updatedAt": "2023-12-01T10:01:00.000Z"
    }
  ],
  "pagination": {
    "current": 1,
    "pages": 10,
    "total": 500
  }
}
```

#### Send Message
```http
POST /messages
```

**Headers:**
```
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "conversationId": "conversation-id",
  "content": "Hello everyone!",
  "type": "text",
  "replyTo": "message-id"
}
```

**Response:**
```json
{
  "message": "Message sent successfully",
  "data": {
    "id": "message-id",
    "conversation": "conversation-id",
    "sender": {
      "id": "user-id",
      "username": "johndoe",
      "firstName": "John",
      "lastName": "Doe",
      "avatar": "/uploads/avatars/avatar.jpg"
    },
    "content": "Hello everyone!",
    "type": "text",
    "file": null,
    "replyTo": {
      "id": "replied-message-id",
      "content": "Previous message",
      "sender": {
        "username": "janedoe",
        "firstName": "Jane",
        "lastName": "Doe"
      },
      "type": "text"
    },
    "edited": {
      "isEdited": false,
      "editedAt": null,
      "originalContent": null
    },
    "reactions": [],
    "readBy": [],
    "deliveredTo": [],
    "status": "sent",
    "isDeleted": false,
    "createdAt": "2023-12-01T10:00:00.000Z",
    "updatedAt": "2023-12-01T10:00:00.000Z"
  }
}
```

#### Send File Message
```http
POST /messages/file
```

**Headers:**
```
Authorization: Bearer <token>
Content-Type: multipart/form-data
```

**Request Body:**
```
Form data:
- conversationId: "conversation-id"
- replyTo: "message-id" (optional)
- file: [file data]
```

**Response:**
```json
{
  "message": "File sent successfully",
  "data": {
    "id": "message-id",
    "conversation": "conversation-id",
    "sender": {
      "id": "user-id",
      "username": "johndoe",
      "firstName": "John",
      "lastName": "Doe",
      "avatar": "/uploads/avatars/avatar.jpg"
    },
    "content": "document.pdf",
    "type": "file",
    "file": {
      "filename": "1638360000000-document.pdf",
      "originalName": "document.pdf",
      "mimetype": "application/pdf",
      "size": 1024000,
      "path": "/uploads/files/1638360000000-document.pdf",
      "url": "/uploads/files/1638360000000-document.pdf"
    },
    "replyTo": null,
    "edited": {
      "isEdited": false,
      "editedAt": null,
      "originalContent": null
    },
    "reactions": [],
    "readBy": [],
    "deliveredTo": [],
    "status": "sent",
    "isDeleted": false,
    "createdAt": "2023-12-01T10:00:00.000Z",
    "updatedAt": "2023-12-01T10:00:00.000Z"
  }
}
```

## WebSocket Events

The application uses Socket.io for real-time communication. Connect to the WebSocket server with authentication:

```javascript
const socket = io('http://localhost:5000', {
  auth: {
    token: 'your-jwt-token'
  }
});
```

### Client Events (Emit)

#### Join Conversation
```javascript
socket.emit('join_conversation', conversationId);
```

#### Leave Conversation
```javascript
socket.emit('leave_conversation', conversationId);
```

#### Send Message
```javascript
socket.emit('send_message', {
  conversationId: 'conversation-id',
  content: 'Hello!',
  type: 'text',
  replyTo: 'message-id' // optional
});
```

#### Typing Indicators
```javascript
// Start typing
socket.emit('typing_start', { conversationId: 'conversation-id' });

// Stop typing
socket.emit('typing_stop', { conversationId: 'conversation-id' });
```

#### Mark Message as Read
```javascript
socket.emit('mark_message_read', {
  messageId: 'message-id',
  conversationId: 'conversation-id'
});
```

#### Change Status
```javascript
socket.emit('status_change', { status: 'away' });
```

### Server Events (Listen)

#### New Message
```javascript
socket.on('new_message', (data) => {
  console.log('New message:', data.message);
  console.log('In conversation:', data.conversationId);
});
```

#### Message Read
```javascript
socket.on('message_read', (data) => {
  console.log('Message read:', data.messageId);
  console.log('By user:', data.userId);
  console.log('At:', data.readAt);
});
```

#### User Typing
```javascript
socket.on('user_typing', (data) => {
  console.log('User typing:', data.username);
  console.log('Is typing:', data.isTyping);
  console.log('In conversation:', data.conversationId);
});
```

#### User Status Change
```javascript
socket.on('user_status_change', (data) => {
  console.log('User status changed:', data.userId);
  console.log('New status:', data.status);
  console.log('Last seen:', data.lastSeen);
});
```

#### Connection Events
```javascript
socket.on('connect', () => {
  console.log('Connected to server');
});

socket.on('disconnect', () => {
  console.log('Disconnected from server');
});

socket.on('error', (error) => {
  console.error('Socket error:', error);
});
```

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Default**: 100 requests per 15 minutes per IP
- **Headers**: Rate limit information is included in response headers:
  - `X-RateLimit-Limit`: Request limit
  - `X-RateLimit-Remaining`: Remaining requests
  - `X-RateLimit-Reset`: Reset time

## File Upload Limits

- **Maximum file size**: 10MB (configurable)
- **Allowed file types**: Images (jpg, png, gif), Documents (pdf, doc, docx, txt), Archives (zip), Media (mp4, mp3, wav)
- **Storage**: Files are stored in `/uploads` directory with unique names

## Error Handling

All endpoints return consistent error responses:

```json
{
  "message": "Error description",
  "errors": [
    {
      "field": "email",
      "message": "Email is required"
    }
  ]
}
```

Common error scenarios:
- **Validation errors**: Invalid input data
- **Authentication errors**: Missing or invalid token
- **Authorization errors**: Insufficient permissions
- **Not found errors**: Resource doesn't exist
- **Rate limit errors**: Too many requests
