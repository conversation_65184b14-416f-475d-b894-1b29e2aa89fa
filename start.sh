#!/bin/bash

echo "========================================"
echo "Starting Internal Messenger"
echo "========================================"
echo

echo "Checking if .env file exists..."
if [ ! -f "server/.env" ]; then
    echo "Warning: server/.env file not found!"
    echo "Please copy server/.env.example to server/.env and configure it."
    echo
    exit 1
fi

echo "Starting the application..."
echo
echo "Frontend will be available at: http://localhost:3000"
echo "Backend API will be available at: http://localhost:5000"
echo
echo "Press Ctrl+C to stop the application"
echo

npm run dev
